import { integer, sqliteTable, text } from "drizzle-orm/sqlite-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";

import { generateId } from "@/lib/helpers";
import { candidate } from "./candidate.entity";
import { jobVacancy } from "./job-vacancy.entity";

export const jobApplication = sqliteTable("job_application", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => generateId()),
  candidateId: text("candidate_id")
    .notNull()
    .references(() => candidate.id, { onDelete: "cascade" }),
  jobVacancyId: text("job_vacancy_id")
    .notNull()
    .references(() => jobVacancy.id, { onDelete: "cascade" }),
  resume: text("resume"),
  coverLetter: text("cover_letter"),
  notes: text("notes"),
  status: text("status", { enum: ["reviewed", "in_process", "closed"] })
    .notNull()
    .default("in_process"),
  createdAt: integer("created_at", { mode: "timestamp" })
    .notNull()
    .$defaultFn(() => new Date()),
  updatedAt: integer("updated_at", { mode: "timestamp" })
    .notNull()
    .$defaultFn(() => new Date())
    .$onUpdate(() => new Date()),
});

export const insertJobApplicationSchema = createInsertSchema(jobApplication);
export const selectJobApplicationSchema = createSelectSchema(jobApplication);
export const patchJobApplicationSchema = createSelectSchema(jobApplication)
  .partial()
  .pick({
    status: true,
  });

export type JobApplication = typeof jobApplication.$inferSelect;
export type NewJobApplication = typeof jobApplication.$inferInsert;
