import { integer, sqliteTable, text } from "drizzle-orm/sqlite-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { z } from "zod";

import { generateId } from "@/lib/helpers";
import { user } from "./user.entity";

// Candidate statuses
export const CANDIDATE_STATUSES = [
  "Новий",
  "Підбір вакансії",
  "Заповнює резюме",
  "Внутрішня співбесіда",
  "Очікуємо на об'єкті",
  "Працює",
  "Запрошення для візи",
  "Передано EU",
  "Передано роботодацю",
  "Не підходить",
] as const;

// Candidate sources
export const CANDIDATE_SOURCES = [
  "facebook",
  "instagram",
  "google",
  "чат",
] as const;

export const candidate = sqliteTable("candidate", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => generateId()),

  // Basic info
  fullName: text("full_name").notNull(),
  phoneNumbers: text("phone_numbers", { mode: "json" })
    .$type<string[]>()
    .notNull(),
  age: integer("age"),
  citizenship: text("citizenship"),

  // Professional info
  specialization: text("specialization"),
  specialtyExperience: text("specialty_experience"),
  foreignExperience: text("foreign_experience"),
  totalExperience: text("total_experience"),
  languageSkills: text("language_skills"),
  drivingLicense: text("driving_license"),
  travelDocument: text("travel_document"),

  // Current status
  currentCountry: text("current_country"),
  currentJob: text("current_job"),
  interestedCountry: text("interested_country"),
  availabilityDate: text("availability_date"),

  // Application management
  status: text("status", { enum: CANDIDATE_STATUSES })
    .notNull()
    .default("Новий"),
  statusComment: text("status_comment"),
  responsibleManagerId: text("responsible_manager_id").references(
    () => user.id,
  ),
  interestedVacancies: text("interested_vacancies", { mode: "json" })
    .$type<string[]>()
    .default([]),
  candidateComment: text("candidate_comment"),
  source: text("source", { enum: CANDIDATE_SOURCES }),
  referral: text("referral"),

  createdAt: integer("created_at", { mode: "timestamp" })
    .notNull()
    .$defaultFn(() => new Date()),
  updatedAt: integer("updated_at", { mode: "timestamp" })
    .notNull()
    .$defaultFn(() => new Date())
    .$onUpdate(() => new Date()),
});

export const insertCandidateSchema = createInsertSchema(candidate, {
  phoneNumbers: z.array(z.string().min(1)),
  interestedVacancies: z.array(z.string()).optional(),
});

export const selectCandidateSchema = createSelectSchema(candidate);

export const patchCandidateSchema = selectCandidateSchema.partial().omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export type Candidate = typeof candidate.$inferSelect;
export type NewCandidate = typeof candidate.$inferInsert;
