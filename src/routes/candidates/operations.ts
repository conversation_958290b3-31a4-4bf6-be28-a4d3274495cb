import type { NewCandidate } from "@/db/schema";

import db from "@/db";
import { candidate } from "@/db/schema";
import { count, eq } from "drizzle-orm";

export class CandidateOperations {
  static async findMany(params?: {
    page?: number;
    limit?: number;
    sortBy?: "createdAt" | "updatedAt";
    sortDirection?: "asc" | "desc";
  }) {
    const {
      page,
      limit,
      sortBy = "createdAt",
      sortDirection = "desc",
    } = params ?? {};

    const [candidates, [{ total }]] = await Promise.all([
      db.query.candidate.findMany({
        limit,
        offset: limit && page ? (page - 1) * limit : undefined,
        orderBy: {
          [sortBy]: sortDirection,
        },
      }),
      db
        .select({
          total: count(),
        })
        .from(candidate)
        .limit(1),
    ]);

    return {
      total,
      page,
      limit,
      pages: limit ? Math.ceil(total / limit) : undefined,
      items: candidates,
    };
  }

  static async findById(id: string) {
    return db.query.candidate.findFirst({
      where: { id },
    });
  }

  static async findByIdWithApplications(id: string) {
    return db.query.candidate.findFirst({
      where: { id },
      with: {
        jobApplications: {
          with: {
            jobVacancy: true,
          },
        },
      },
    });
  }

  static async findByContactInfo(phoneNumber: string, email?: string) {
    let existingCandidate = await db.query.candidate.findFirst({
      where: {
        phoneNumber,
      },
    });

    if (!existingCandidate && email) {
      existingCandidate = await db.query.candidate.findFirst({
        where: {
          email,
        },
      });
    }

    return existingCandidate;
  }

  static async findOrCreate(candidateData: NewCandidate) {
    const normalizedData = {
      ...candidateData,
      email: candidateData.email?.toLowerCase(),
      phoneNumber: candidateData.phoneNumber.replace(/[\s\-\(\)]/g, ""),
    };

    const existingCandidate = await CandidateOperations.findByContactInfo(
      normalizedData.phoneNumber,
      normalizedData.email,
    );

    if (existingCandidate) {
      const updatedCandidate = await CandidateOperations.update(
        existingCandidate.id,
        {
          fullName: normalizedData.fullName,
          email: normalizedData.email,
          phoneNumber: normalizedData.phoneNumber,
          citizenship: normalizedData.citizenship,
        },
      );
      return updatedCandidate;
    }

    return CandidateOperations.create(normalizedData);
  }

  static async create(data: NewCandidate) {
    const [newCandidate] = await db.insert(candidate).values(data).returning();
    return newCandidate;
  }

  static async update(id: string, updates: Partial<NewCandidate>) {
    const [updated] = await db
      .update(candidate)
      .set({
        ...updates,
        updatedAt: new Date(),
      })
      .where(eq(candidate.id, id))
      .returning();
    return updated;
  }

  static async delete(id: string) {
    const [deleted] = await db
      .delete(candidate)
      .where(eq(candidate.id, id))
      .returning();
    return !!deleted;
  }
}
