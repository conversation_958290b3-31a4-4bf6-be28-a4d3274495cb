import { selectCandidateSchema, selectJobApplicationSchema } from "@/db/schema";
import { createPaginatedSchema, paginationParamsSchema } from "@/lib/schemas";
import { z } from "zod";

export const createJobApplicationDTO = z.object({
  jobVacancyId: z.string().min(1),
  resume: z.string().optional(),
  coverLetter: z.string().optional(),
  notes: z.string().optional(),
  status: z.enum(["reviewed", "in_process", "closed"]).optional(),

  fullName: z.string().min(1),
  email: z.string().email(),
  phoneNumber: z.string().min(1),
  citizenship: z.string().min(1),
});

export const jobApplicationWithCandidateDTO = selectJobApplicationSchema
  .omit({
    candidateId: true,
  })
  .extend({
    candidate: selectCandidateSchema,
  });

export const listApplicationsDTO = createPaginatedSchema(
  jobApplicationWithCandidateDTO,
);
export const listApplicationsQueryDTO = paginationParamsSchema.partial();

export type CreateJobApplicationDTO = z.infer<typeof createJobApplicationDTO>;
export type JobApplicationWithCandidate = z.infer<
  typeof jobApplicationWithCandidateDTO
>;
