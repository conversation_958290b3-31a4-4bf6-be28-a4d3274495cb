import {
  APPLICATION_SOURCES,
  APPLICATION_STATUSES,
  APPLICATION_TYPES,
  selectCandidateSchema,
  selectJobApplicationSchema,
} from "@/db/schema";
import { createPaginatedSchema, paginationParamsSchema } from "@/lib/schemas";
import { z } from "zod";

export const createJobApplicationDTO = z.object({
  jobVacancyId: z.string().min(1).optional(),
  createdDate: z.string().min(1),
  citizenship: z.string().min(1),
  fullName: z.string().min(1),
  phoneNumber: z.string().min(1),
  status: z.enum(APPLICATION_STATUSES).optional(),
  applicationType: z.enum(APPLICATION_TYPES),
  source: z.enum(APPLICATION_SOURCES).optional(),
  vacancyLink: z.string().optional(),
  referral: z.string().optional(),
  comment: z.string().optional(),
  responsibleManagerId: z.string().optional(),
});

export const jobApplicationWithCandidateDTO = selectJobApplicationSchema
  .omit({
    candidateId: true,
  })
  .extend({
    candidate: selectCandidateSchema,
  });

export const listApplicationsDTO = createPaginatedSchema(
  jobApplicationWithCandidateDTO,
);
export const listApplicationsQueryDTO = paginationParamsSchema.partial();

export type CreateJobApplicationDTO = z.infer<typeof createJobApplicationDTO>;
export type JobApplicationWithCandidate = z.infer<
  typeof jobApplicationWithCandidateDTO
>;
