import { appClient } from "@/app";
import env from "@/env";
import {
  cleanupTestDb,
  setupTestDb,
  setupTestUser,
} from "@/lib/utils/test.utils";
import { afterAll, beforeAll, describe, expect, it } from "vitest";

if (env.NODE_ENV !== "test") {
  throw new Error("NODE_ENV must be 'test'");
}

describe("job applications routes", () => {
  const testVacancy = {
    id: "test-job-id",
    country: "Germany",
    cities: ["Berlin", "Munich"],
    specialty: "Software Engineering",
    experience: "not_required" as const,
    salary: "70000-90000",
    genders: ["male" as const, "female" as const],
    jobType: "Permanent",
    status: "draft" as const,
    specialties: ["others" as const],
    title: {
      en: "Senior Software Engineer",
      de: "Senior Softwareentwickler",
    },
    workwear: {
      en: "Business casual",
      de: "Business-Casual",
    },
    responsibilities: {
      en: "Leading development team",
      de: "Entwicklungsteam leiten",
    },
    description: {
      en: "Looking for experienced developer",
      de: "Suchen erfahrenen Entwickler",
    },
    workSchedule: {
      en: "Full-time",
      de: "Vollzeit",
    },
    languageRequirements: [],
    numberOfPositions: 2,
    housing: "provided" as const,
  };

  const createTestApplication = (id: string) => ({
    fullName: `John Doe ${id}`,
    email: `john${id}@example.com`,
    phoneNumber: "+1234567890",
    citizenship: "USA",
    resume: "resume.pdf",
    coverLetter: "I am interested in this position",
    status: "in_process" as const,
    jobVacancyId: testVacancy.id,
  });

  let addAuthHeaders: () => Record<string, string>;

  beforeAll(async () => {
    setupTestDb();
    const auth = await setupTestUser("author");
    addAuthHeaders = auth.addAuthHeaders;

    try {
      await appClient.jobs.$post(
        {
          json: {
            ...testVacancy,
          },
        },
        {
          headers: addAuthHeaders(),
        },
      );
    } catch (e) {
      console.error("Failed to create test vacancy", e);
      throw e;
    }
  });

  afterAll(() => {
    cleanupTestDb();
  });

  describe("GET /applications", () => {
    beforeAll(async () => {
      // Create multiple test applications
      await Promise.all([
        appClient.applications.$post({
          json: createTestApplication("app1"),
        }),
        appClient.applications.$post({
          json: createTestApplication("app2"),
        }),
        appClient.applications.$post({
          json: createTestApplication("app3"),
        }),
      ]);
    });

    it("lists applications for author with pagination", async () => {
      const response = await appClient.applications.$get(
        {
          query: {
            page: "1",
            limit: "2",
            sortBy: "createdAt",
            sortDirection: "desc",
          },
        },
        {
          headers: addAuthHeaders(),
        },
      );

      expect(response.status).toBe(200);
      const json = await response.json();
      expect(json).toMatchObject({
        items: expect.any(Array),
        total: 3,
        page: 1,
        limit: 2,
        pages: 2,
      });
      expect(json.items).toHaveLength(2);
    });

    it("returns all applications when no pagination params", async () => {
      const response = await appClient.applications.$get(
        {},
        {
          headers: addAuthHeaders(),
        },
      );

      expect(response.status).toBe(200);
      const json = await response.json();
      expect(json.items).toHaveLength(3);
      expect(json.page).toBeUndefined();
      expect(json.limit).toBeUndefined();
      expect(json.pages).toBeUndefined();
    });
  });

  it("post /job-applications creates an application without auth", async () => {
    const testApplication = createTestApplication("new");
    const response = await appClient.applications.$post({
      json: testApplication,
    });

    expect(response.status).toBe(201);
    const json = await response.json();
    expect(json).toMatchObject({
      status: "in_process",
      candidate: {
        fullName: testApplication.fullName,
        email: testApplication.email,
        phoneNumber: testApplication.phoneNumber,
        citizenship: testApplication.citizenship,
      },
    });
  });

  it("patch /job-applications/{id} updates application status", async () => {
    const testApplication = createTestApplication("patch-test");
    const createResponse = await appClient.applications.$post({
      json: testApplication,
    });

    expect(createResponse.status).toBe(201);
    const createdApplication = await createResponse.json();

    // Type guard to ensure we have the correct response type
    if ("id" in createdApplication) {
      const response = await appClient.applications[":id"].$patch(
        {
          param: { id: createdApplication.id },
          json: { status: "reviewed" },
        },
        {
          headers: addAuthHeaders(),
        },
      );

      expect(response.status).toBe(200);
      const json = await response.json();
      if ("status" in json) {
        expect(json.status).toBe("reviewed");
      }
    } else {
      throw new Error("Failed to create application for patch test");
    }
  });
});
