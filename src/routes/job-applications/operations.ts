import type { NewJobApplication } from "@/db/schema";

import db from "@/db";
import { jobApplication } from "@/db/schema";
import { count, eq } from "drizzle-orm";
import { CandidateOperations } from "../candidates/operations";

export class JobApplicationOperations {
  static async findMany(
    userId: string,
    role: string,
    params?: {
      page?: number;
      limit?: number;
      sortBy?: "createdAt" | "updatedAt";
      sortDirection?: "asc" | "desc";
    },
  ) {
    const {
      page,
      limit,
      sortBy = "createdAt",
      sortDirection = "desc",
    } = params ?? {};

    const [applications, [{ total }]] = await Promise.all([
      db.query.jobVacancy.findMany({
        where: role !== "admin" ? { authorId: userId } : undefined,
        with: {
          jobApplications: {
            limit,
            offset: limit && page ? (page - 1) * limit : undefined,
            orderBy: {
              [sortBy]: sortDirection,
            },
            with: {
              candidate: true,
            },
            columns: {
              candidateId: false,
            },
          },
        },
      }),
      db
        .select({
          total: count(),
        })
        .from(jobApplication)
        .limit(1),
    ]);

    const flatApplications = applications.flatMap(
      (vacancy) => vacancy.jobApplications,
    );

    return {
      total,
      page,
      limit,
      pages: limit ? Math.ceil(total / limit) : undefined,
      items: flatApplications,
    };
  }

  static async findById(id: string) {
    return db.query.jobApplication.findFirst({
      where: { id },
    });
  }

  static async findByIdWithVacancy(id: string) {
    return db.query.jobApplication.findFirst({
      where: { id },
      with: {
        candidate: true,
        jobVacancy: true,
      },
    });
  }

  static async findManyByVacancyId(vacancyId: string) {
    return db.query.jobApplication.findMany({
      where: { jobVacancyId: vacancyId },
      with: {
        candidate: true,
      },
    });
  }

  /**
   * Create application with candidate data (smart candidate matching)
   * This is the main method for handling job application submissions
   */
  static async createWithCandidateData(applicationData: {
    jobVacancyId: string;
    resume?: string;
    coverLetter?: string;
    notes?: string;
    status?: "reviewed" | "in_process" | "closed";
    candidateData: {
      fullName: string;
      email: string;
      phoneNumber: string;
      citizenship: string;
    };
  }) {
    // Find or create candidate
    const candidate = await CandidateOperations.findOrCreate(
      applicationData.candidateData,
    );

    // Create application with resume and cover letter specific to this application
    const application = await JobApplicationOperations.create({
      candidateId: candidate.id,
      jobVacancyId: applicationData.jobVacancyId,
      resume: applicationData.resume,
      coverLetter: applicationData.coverLetter,
      notes: applicationData.notes,
      status: applicationData.status || "in_process",
    });

    return {
      ...application,
      candidate,
    };
  }

  static async create(data: NewJobApplication) {
    const [newJobApplication] = await db
      .insert(jobApplication)
      .values(data)
      .returning();
    return newJobApplication;
  }

  static async update(id: string, updates: Partial<NewJobApplication>) {
    const [updated] = await db
      .update(jobApplication)
      .set({
        ...updates,
        updatedAt: new Date(),
      })
      .where(eq(jobApplication.id, id))
      .returning();
    return updated;
  }

  static async delete(id: string) {
    const [deleted] = await db
      .delete(jobApplication)
      .where(eq(jobApplication.id, id))
      .returning();
    return !!deleted;
  }
}
