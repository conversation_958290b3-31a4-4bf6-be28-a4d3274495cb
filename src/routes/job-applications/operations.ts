import type { NewJobApplication } from "@/db/schema";

import db from "@/db";
import {
  type APPLICATION_SOURCES,
  type APPLICATION_STATUSES,
  type APPLICATION_TYPES,
  jobApplication,
} from "@/db/schema";
import { count, eq } from "drizzle-orm";
import { CandidateOperations } from "../candidates/operations";

export class JobApplicationOperations {
  static async findMany(
    userId: string,
    role: string,
    params?: {
      page?: number;
      limit?: number;
      sortBy?: "createdAt" | "updatedAt";
      sortDirection?: "asc" | "desc";
      status?: (typeof APPLICATION_STATUSES)[number];
      applicationType?: (typeof APPLICATION_TYPES)[number];
      source?: (typeof APPLICATION_SOURCES)[number];
      responsibleManagerId?: string;
      search?: string;
      dateFrom?: string;
      dateTo?: string;
    },
  ) {
    const {
      page,
      limit,
      sortBy = "createdAt",
      sortDirection = "desc",
      status,
      applicationType,
      source,
      responsibleManagerId,
      search,
      dateFrom,
      dateTo,
    } = params ?? {};

    // Build where conditions
    const whereConditions: any = {};

    if (status) whereConditions.status = status;
    if (applicationType) whereConditions.applicationType = applicationType;
    if (source) whereConditions.source = source;
    if (responsibleManagerId)
      whereConditions.responsibleManagerId = responsibleManagerId;

    // For non-admin users, filter by their managed applications or authored vacancies
    if (role !== "admin") {
      // This is a simplified approach - in a real app you'd want more sophisticated filtering
      whereConditions.responsibleManagerId = userId;
    }

    const [applications, [{ total }]] = await Promise.all([
      db.query.jobApplication.findMany({
        where: whereConditions,
        limit,
        offset: limit && page ? (page - 1) * limit : undefined,
        orderBy: {
          [sortBy]: sortDirection,
        },
        with: {
          candidate: true,
          jobVacancy: true,
          responsibleManager: {
            columns: {
              id: true,
              name: true,
              image: true,
            },
          },
        },
      }),
      db
        .select({
          total: count(),
        })
        .from(jobApplication)
        .limit(1),
    ]);

    // Apply search filter if provided (done in memory for simplicity)
    let filteredApplications = applications;
    if (search) {
      const searchLower = search.toLowerCase();
      filteredApplications = applications.filter(
        (app) =>
          app.fullName.toLowerCase().includes(searchLower) ||
          app.phoneNumber.includes(search) ||
          app.citizenship.toLowerCase().includes(searchLower) ||
          app.comment?.toLowerCase().includes(searchLower),
      );
    }

    // Apply date filters
    if (dateFrom || dateTo) {
      filteredApplications = filteredApplications.filter((app) => {
        const appDate = new Date(app.createdDate);
        if (dateFrom && appDate < new Date(dateFrom)) return false;
        if (dateTo && appDate > new Date(dateTo)) return false;
        return true;
      });
    }

    return {
      total: search || dateFrom || dateTo ? filteredApplications.length : total,
      page,
      limit,
      pages: limit
        ? Math.ceil(
            (search || dateFrom || dateTo
              ? filteredApplications.length
              : total) / limit,
          )
        : undefined,
      items: filteredApplications,
    };
  }

  static async getStats(userId: string, role: string) {
    const whereConditions: any = {};

    // For non-admin users, filter by their managed applications
    if (role !== "admin") {
      whereConditions.responsibleManagerId = userId;
    }

    const applications = await db.query.jobApplication.findMany({
      where: whereConditions,
    });

    const now = new Date();
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    const stats = {
      totalApplications: applications.length,
      byStatus: {} as Record<string, number>,
      byType: {} as Record<string, number>,
      bySource: {} as Record<string, number>,
      recentApplications: 0,
    };

    applications.forEach((app) => {
      // Count by status
      stats.byStatus[app.status] = (stats.byStatus[app.status] || 0) + 1;

      // Count by type
      stats.byType[app.applicationType] =
        (stats.byType[app.applicationType] || 0) + 1;

      // Count by source
      if (app.source) {
        stats.bySource[app.source] = (stats.bySource[app.source] || 0) + 1;
      }

      // Count recent applications
      if (app.createdAt >= sevenDaysAgo) {
        stats.recentApplications++;
      }
    });

    return stats;
  }

  static async findById(id: string) {
    return db.query.jobApplication.findFirst({
      where: { id },
    });
  }

  static async findByIdWithVacancy(id: string) {
    return db.query.jobApplication.findFirst({
      where: { id },
      with: {
        candidate: true,
        jobVacancy: true,
      },
    });
  }

  static async findManyByVacancyId(vacancyId: string) {
    return db.query.jobApplication.findMany({
      where: { jobVacancyId: vacancyId },
      with: {
        candidate: true,
      },
    });
  }

  /**
   * Create application with candidate data (smart candidate matching)
   * This is the main method for handling job application submissions
   */
  static async createWithCandidateData(applicationData: {
    jobVacancyId?: string;
    createdDate: string;
    citizenship: string;
    fullName: string;
    phoneNumber: string;
    status?: (typeof APPLICATION_STATUSES)[number];
    applicationType: (typeof APPLICATION_TYPES)[number];
    source?: (typeof APPLICATION_SOURCES)[number];
    vacancyLink?: string;
    referral?: string;
    comment?: string;
    responsibleManagerId?: string;
  }) {
    // Find or create candidate
    const candidate = await CandidateOperations.findOrCreate({
      fullName: applicationData.fullName,
      phoneNumber: applicationData.phoneNumber,
      citizenship: applicationData.citizenship,
    });

    // Create application with the new schema
    const application = await JobApplicationOperations.create({
      candidateId: candidate.id,
      jobVacancyId: applicationData.jobVacancyId,
      createdDate: applicationData.createdDate,
      citizenship: applicationData.citizenship,
      fullName: applicationData.fullName,
      phoneNumber: applicationData.phoneNumber,
      status: applicationData.status || "Зв'язатися",
      applicationType: applicationData.applicationType,
      source: applicationData.source,
      vacancyLink: applicationData.vacancyLink,
      referral: applicationData.referral,
      comment: applicationData.comment,
      responsibleManagerId: applicationData.responsibleManagerId,
    });

    return {
      ...application,
      candidate,
    };
  }

  static async create(data: NewJobApplication) {
    const [newJobApplication] = await db
      .insert(jobApplication)
      .values(data)
      .returning();
    return newJobApplication;
  }

  static async update(id: string, updates: Partial<NewJobApplication>) {
    const [updated] = await db
      .update(jobApplication)
      .set({
        ...updates,
        updatedAt: new Date(),
      })
      .where(eq(jobApplication.id, id))
      .returning();
    return updated;
  }

  static async delete(id: string) {
    const [deleted] = await db
      .delete(jobApplication)
      .where(eq(jobApplication.id, id))
      .returning();
    return !!deleted;
  }

  static async bulkUpdate(
    applicationIds: string[],
    updates: {
      status?: typeof APPLICATION_STATUSES[number];
      responsibleManagerId?: string;
      comment?: string;
    }
  ) {
    const results = [];

    for (const id of applicationIds) {
      const updated = await JobApplicationOperations.update(id, updates);
      if (updated) {
        results.push(updated);
      }
    }

    return results;
  }
}
